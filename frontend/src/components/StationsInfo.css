.stations-info {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-header h2 {
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 2rem;
}

.page-header p {
  color: #666;
  font-size: 1.1rem;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
  margin-bottom: 3rem;
}

.countries-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  height: fit-content;
}

.countries-section h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.2rem;
}

.countries-list {
  height: 500px;
  overflow-y: scroll;
}

.country-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  margin-bottom: 0.5rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.country-item:hover {
  border-color: #667eea;
  background: #f8f9ff;
  transform: translateX(4px);
}

.country-item.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateX(4px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.country-name {
  font-weight: 500;
  font-size: 1rem;
}

.station-count {
  font-size: 0.9rem;
  opacity: 0.8;
  font-weight: 600;
}

.station-details {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-selection {
  text-align: center;
  color: #666;
}

.no-selection .icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.no-selection h3 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.loading {
  text-align: center;
  color: #667eea;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  text-align: center;
  color: #ff6b6b;
}

.error .icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.station-info-card {
  width: 100%;
  align-self: flex-start;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e1e5e9;
}

.card-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.total-stations {
  text-align: center;
}

.total-stations .number {
  display: block;
  font-size: 2.5rem;
  font-weight: bold;
  color: #667eea;
  line-height: 1;
}

.total-stations .label {
  font-size: 0.9rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.networks-list h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
  
}

.networks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
}

.network-item {
  background: #f8f9ff;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.2s ease;
}

.network-item:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.network-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.network-city {
  color: #666;
  font-size: 0.9rem;
}

.no-networks {
  color: #666;
  font-style: italic;
  text-align: center;
  padding: 2rem;
}

.summary-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.summary-section h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.3rem;
  text-align: center;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 1px;
}

@media (max-width: 768px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .station-details {
    min-height: 300px;
    padding: 1.5rem;
  }
  
  .card-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .networks-grid {
    grid-template-columns: 1fr;
  }
  
  .summary-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}
