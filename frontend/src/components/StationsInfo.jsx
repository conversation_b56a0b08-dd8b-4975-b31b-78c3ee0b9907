import { useState, useEffect } from 'react'
import axios from 'axios'
import './StationsInfo.css'

function StationsInfo() {
  const [countries, setCountries] = useState([])
  const [selectedCountry, setSelectedCountry] = useState('')
  const [stationCount, setStationCount] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  useEffect(() => {
    fetchCountries()
  }, [])

  const fetchCountries = async () => {
    try {
      const response = await axios.get('http://127.0.0.1:5000/stations/summary')
      const uniqueCountries = response.data.map(item => item.country).sort()
      setCountries(uniqueCountries)
    } catch (err) {
      console.error('Erreur lors du chargement des pays:', err)
      setError('Erreur lors du chargement des pays')
    }
  }

  const fetchStationCount = async (country) => {
    if (!country) return

    try {
      setLoading(true)
      setError(null)
      const response = await axios.get(`http://127.0.0.1:5000/stations?country=${encodeURIComponent(country)}`)
      setStationCount(response.data.total_stations)
    } catch (err) {
      setError('Erreur lors du chargement du nombre de stations')
      console.error('Erreur:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleCountrySelect = (country) => {
    setSelectedCountry(country)
    setStationCount(null)
    fetchStationCount(country)
  }

  return (
    <div className="stations-info">
      <div className="page-header">
        <h2>📊 Informations sur les Stations</h2>
        <p>Sélectionnez un pays pour voir le nombre de stations</p>
      </div>

      <div className="content-grid">
        <div className="countries-section">
          <h3>Choisir un pays</h3>
          {error && !selectedCountry && (
            <div className="error">
              <div className="icon">⚠️</div>
              <p>{error}</p>
            </div>
          )}
          <div className="countries-list">
            {countries.map((country) => (
              <div
                key={country}
                className={`country-item ${selectedCountry === country ? 'selected' : ''}`}
                onClick={() => handleCountrySelect(country)}
              >
                <span className="country-name">{country}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="station-details">
          {!selectedCountry && (
            <div className="no-selection">
              <div className="icon">🗺️</div>
              <h3>Sélectionnez un pays</h3>
              <p>Choisissez un pays dans la liste pour voir le nombre de stations</p>
            </div>
          )}

          {selectedCountry && loading && (
            <div className="loading">
              <div className="spinner"></div>
              <p>Chargement du nombre de stations...</p>
            </div>
          )}

          {selectedCountry && error && (
            <div className="error">
              <div className="icon">⚠️</div>
              <p>{error}</p>
            </div>
          )}

          {selectedCountry && stationCount !== null && !loading && !error && (
            <div className="station-info-card">
              <div className="card-header">
                <h3>🏳️ {selectedCountry}</h3>
                <div className="total-stations">
                  <span className="number">{stationCount}</span>
                  <span className="label">Station(s)</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default StationsInfo
