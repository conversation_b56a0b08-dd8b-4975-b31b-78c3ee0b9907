import { useState, useEffect } from 'react'
import axios from 'axios'
import './StationsInfo.css'

function StationsInfo() {
  const [countries, setCountries] = useState([])
  const [selectedCountry, setSelectedCountry] = useState('')
  const [stationInfo, setStationInfo] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [summary, setSummary] = useState([])

  useEffect(() => {
    fetchCountriesSummary()
  }, [])

  const fetchCountriesSummary = async () => {
    try {
      const response = await axios.get('http://127.0.0.1:5000/stations/summary')
      setSummary(response.data)
      const uniqueCountries = response.data.map(item => item.country).sort()
      setCountries(uniqueCountries)
    } catch (err) {
      console.error('Erreur lors du chargement du résumé:', err)
    }
  }

  const fetchStationsForCountry = async (country) => {
    if (!country) return

    try {
      setLoading(true)
      setError(null)
      const response = await axios.get(`http://127.0.0.1:5000/stations?country=${encodeURIComponent(country)}`)
      setStationInfo(response.data)
    } catch (err) {
      setError('Erreur lors du chargement des informations de stations')
      console.error('Erreur:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleCountrySelect = (country) => {
    setSelectedCountry(country)
    fetchStationsForCountry(country)
  }

  const getStationCountForCountry = (country) => {
    const countryData = summary.find(item => item.country === country)
    return countryData ? countryData.stations : 0
  }

  return (
    <div className="stations-info">
      <div className="page-header">
        <h2>📊 Informations sur les Stations</h2>
        <p>Consultez le nombre de stations par pays</p>
      </div>

      <div className="content-grid">
        <div className="countries-section">
          <h3>Sélectionner un pays</h3>
          <div className="countries-list">
            {countries.map((country) => (
              <div
                key={country}
                className={`country-item ${selectedCountry === country ? 'selected' : ''}`}
                onClick={() => handleCountrySelect(country)}
              >
                <span className="country-name">{country}</span>
                <span className="station-count">
                  {getStationCountForCountry(country)} station(s)
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="station-details">
          {!selectedCountry && (
            <div className="no-selection">
              <div className="icon">🗺️</div>
              <h3>Sélectionnez un pays</h3>
              <p>Choisissez un pays dans la liste pour voir le nombre de stations</p>
            </div>
          )}

          {selectedCountry && loading && (
            <div className="loading">
              <div className="spinner"></div>
              <p>Chargement des informations...</p>
            </div>
          )}

          {selectedCountry && error && (
            <div className="error">
              <div className="icon">⚠️</div>
              <p>{error}</p>
            </div>
          )}

          {selectedCountry && stationInfo && !loading && (
            <div className="station-info-card">
              <div className="card-header">
                <h3>🏳️ {stationInfo.country}</h3>
                <div className="total-stations">
                  <span className="number">{stationInfo.total_stations}</span>
                  <span className="label">Station(s)</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default StationsInfo
